<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue'
import * as d3 from 'd3'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'

interface NetworkNode extends AnalysisNode {
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
  index?: number;
}

interface NetworkLink extends NodeConnection {
  source: NetworkNode | string | number;
  target: NetworkNode | string | number;
  index?: number;
}

interface Props {
  nodes: AnalysisNode[];
  connections: NodeConnection[];
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'nodes-updated': [nodes: AnalysisNode[]]
  'connections-updated': [connections: NodeConnection[]]
}>()

// 本地存储相关
const getExtendedStorageKey = () => {
  const url = new URL(window.location.href)
  const topic = url.searchParams.get('topic') || 'unknown'
  const thinkingPrompt = url.searchParams.get('thinkingPrompt') || ''
  return `deductive_extended_${topic}_${thinkingPrompt}`.replace(/[^a-zA-Z0-9_]/g, '_').substring(0, 100)
}

const saveExtendedDataToStorage = (nodes: AnalysisNode[], connections: NodeConnection[]) => {
  try {
    const key = getExtendedStorageKey()
    const data = {
      timestamp: Date.now(),
      nodes: nodes,
      connections: connections
    }
    localStorage.setItem(key, JSON.stringify(data))
    console.log('DeductiveTreeGraph: Saved extended data to storage')
  } catch (e) {
    console.warn('Failed to save extended data to storage:', e)
  }
}

const loadExtendedDataFromStorage = () => {
  try {
    const key = getExtendedStorageKey()
    const stored = localStorage.getItem(key)
    if (stored) {
      const data = JSON.parse(stored)
      // 检查数据是否过期（7天）
      const now = Date.now()
      const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
      if (now - data.timestamp < maxAge) {
        return { nodes: data.nodes, connections: data.connections }
      } else {
        // 清除过期数据
        localStorage.removeItem(key)
      }
    }
  } catch (e) {
    console.warn('Failed to load extended data from storage:', e)
  }
  return null
}

const svgRef = ref<SVGElement | null>(null)
const currentStep = ref(0)
const history = ref<{ nodes: NetworkNode[], links: NetworkLink[] }[]>([])
const maxSteps = ref(0)
const editingNode = ref<string | null>(null)
const editingText = ref('')
const hoveredNode = ref<string | null>(null)
const selectedNode = ref<string | null>(null)
const showNodeActions = ref(false)
const nodeActionPosition = ref({ x: 0, y: 0 })
const zoomBehavior = ref<any>(null)
const currentZoom = ref(1)
const isGenerating = ref(false)
const renderTimeout = ref<number | null>(null)
const showNodeList = ref(true)

// What If 功能相关状态
const showWhatIfDialog = ref(false)
const whatIfBaseNode = ref<NetworkNode | null>(null)
const whatIfScenario = ref('')
const isWhatIfGenerating = ref(false)

// 动态获取容器尺寸
const getContainerSize = () => {
  if (!svgRef.value) return { width: 1200, height: 800 }

  // 获取SVG父容器的尺寸
  const parentElement = svgRef.value.parentElement
  if (parentElement) {
    const rect = parentElement.getBoundingClientRect()
    if (rect.width > 0 && rect.height > 0) {
      return {
        width: rect.width,
        height: rect.height
      }
    }
  }

  // 回退到SVG自身尺寸
  const rect = svgRef.value.getBoundingClientRect()
  return {
    width: rect.width || 1200,
    height: rect.height || 800
  }
}





// 拖拽事件处理已在渲染函数中内联定义

// 节点交互事件处理
const handleNodeMouseOver = (event: any, d: NetworkNode) => {
  hoveredNode.value = d.id
  // 获取SVG相对位置
  const svgRect = svgRef.value!.getBoundingClientRect()
  const nodeRect = (event.target as Element).getBoundingClientRect()
  nodeActionPosition.value = {
    x: nodeRect.left - svgRect.left + nodeRect.width / 2,
    y: nodeRect.top - svgRect.top + nodeRect.height + 10
  }

  // 延迟显示菜单，避免快速移动时闪烁
  setTimeout(() => {
    if (hoveredNode.value === d.id) {
      showNodeActions.value = true
    }
  }, 300)
}

const handleNodeMouseOut = (event: any, d: NetworkNode) => {
  // 检查鼠标是否移动到菜单上
  const relatedTarget = event.relatedTarget
  if (relatedTarget && relatedTarget.closest('.node-actions-menu')) {
    return // 不隐藏菜单
  }

  setTimeout(() => {
    if (hoveredNode.value === d.id && !showNodeActions.value) {
      hoveredNode.value = null
    }
  }, 200)
}

const handleNodeDoubleClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  showNodeActions.value = false
  editingNode.value = d.id
  editingText.value = d.title
}

const handleNodeClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  // 设置选中节点
  selectedNode.value = d.id

  // 滚动到列表中对应的项
  scrollToListItem(d.id)

  if (hoveredNode.value === d.id && !showNodeActions.value) {
    showNodeActions.value = true
  }
}

// 菜单交互处理
const handleMenuMouseEnter = () => {
  // 保持菜单显示
}

const handleMenuMouseLeave = () => {
  showNodeActions.value = false
  hoveredNode.value = null
}

// 缩放控制函数
const zoomIn = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1.5)
  }
}

const zoomOut = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1 / 1.5)
  }
}

const resetZoom = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(500)
      .call(zoomBehavior.value.transform, d3.zoomIdentity)
  }
}

// 验证和清理网络数据
const validateNetworkData = (data: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  const nodeIds = new Set(data.nodes.map(n => n.id))

  // 过滤掉引用不存在节点的连接
  const validLinks = data.links.filter(link => {
    const sourceId = typeof link.source === 'string' ? link.source : link.from
    const targetId = typeof link.target === 'string' ? link.target : link.to

    const isValid = nodeIds.has(sourceId) && nodeIds.has(targetId)
    if (!isValid) {
      console.warn('DeductiveTreeGraph: Removing invalid link:', { sourceId, targetId, availableNodes: Array.from(nodeIds) })
    }
    return isValid
  })

  return {
    nodes: data.nodes,
    links: validLinks
  }
}

// 神经网络样式的力导向图渲染
const renderNetwork = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  console.log('DeductiveTreeGraph: Rendering network with data:', networkData);
  if (!svgRef.value || !networkData.nodes.length) {
    console.warn('DeductiveTreeGraph: Cannot render - missing svgRef or nodes');
    return
  }

  try {
    // 验证和清理数据
    const validatedData = validateNetworkData(networkData)
    console.log('DeductiveTreeGraph: Validated data:', validatedData);

    // 获取容器尺寸
    const { width, height } = getContainerSize()
    console.log('DeductiveTreeGraph: Container size:', { width, height });

    d3.select(svgRef.value).selectAll('*').remove()

    const svg = d3.select(svgRef.value)
      .attr("width", "100%")
      .attr("height", "100%")
      .attr("viewBox", `0 0 ${width} ${height}`)

    console.log('DeductiveTreeGraph: SVG setup complete, viewBox:', `0 0 ${width} ${height}`);

    // 添加缩放和平移功能
    const zoom = d3.zoom()
      .scaleExtent([0.1, 4])
      .on("zoom", (event: any) => {
        container.attr("transform", event.transform)
        currentZoom.value = event.transform.k
      })

    svg.call(zoom as any)
    zoomBehavior.value = zoom

    // 创建容器组
    const container = svg.append("g")
      .attr("class", "zoom-container")

    // 创建力导向仿真
    const simulation = d3.forceSimulation(validatedData.nodes)
      .force("link", d3.forceLink(validatedData.links).id((d: any) => d.id).distance(120).strength(0.5))
      .force("charge", d3.forceManyBody().strength(-400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius((d: any) => {
        // 使用与显示相同的节点大小计算逻辑
        const title = d.title || '节点'
        const baseSize = d.type === 'initial' ? 30 : 25
        const textLength = title.length
        const sizeAdjustment = Math.min(textLength * 1.2, 15)
        const nodeRadius = Math.max(baseSize, Math.min(baseSize + sizeAdjustment, 50))
        return nodeRadius + 8 // 添加一些间距
      }))

    // 创建连接线
    const links = container.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(validatedData.links)
      .enter().append("line")
      .attr("stroke", (d: any) => {
        const sourceType = (d.source as NetworkNode).type
        const targetType = (d.target as NetworkNode).type
        if (sourceType === 'positive' || targetType === 'positive') return '#22c55e'
        if (sourceType === 'negative' || targetType === 'negative') return '#ef4444'
        return '#94a3b8'
      })
      .attr("stroke-width", (d: any) => Math.max(1, d.strength * 3))
      .attr("stroke-opacity", 0.6)

    // 创建节点组
    const nodeGroups = container.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(validatedData.nodes)
      .enter().append("g")
      .attr("class", "node-group")
      .style("cursor", "grab")
      .call(d3.drag<any, NetworkNode>()
        .on("start", (event: any, d: NetworkNode) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
          d3.select(event.currentTarget).style("cursor", "grabbing")
        })
        .on("drag", (event: any, d: NetworkNode) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on("end", (event: any, d: NetworkNode) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
          d3.select(event.currentTarget).style("cursor", "grab")
        }))

    // 计算节点大小的函数
    const getNodeRadius = (d: any) => {
      const title = d.title || '节点'
      const baseSize = d.type === 'initial' ? 30 : 25
      // 根据文字长度动态调整大小，但设置最小和最大值
      const textLength = title.length
      const sizeAdjustment = Math.min(textLength * 1.2, 15)
      return Math.max(baseSize, Math.min(baseSize + sizeAdjustment, 50))
    }

    // 获取显示文本的函数
    const getDisplayText = (d: any) => {
      const title = d.title || '节点'
      const radius = getNodeRadius(d)
      // 根据圆圈大小动态调整显示的文字长度
      const maxChars = Math.floor(radius / 4)
      if (title.length <= maxChars) {
        return title
      }
      return title.substring(0, maxChars - 1) + '…'
    }

    // 节点背景圆圈（自适应大小）
    nodeGroups.append("circle")
      .attr("r", getNodeRadius)
      .attr("fill", (d: any) => {
        if (d.type === 'initial') return '#6366f1'
        if (d.type === 'positive') return '#10b981'
        if (d.type === 'negative') return '#ef4444'
        return '#64748b'
      })
      .attr("stroke", (d: any) => {
        // 选中节点显示特殊边框
        return selectedNode.value === d.id ? '#fbbf24' : '#fff'
      })
      .attr("stroke-width", (d: any) => {
        // 选中节点边框更粗
        return selectedNode.value === d.id ? 3 : 1.5
      })
      .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")

    // 节点文本（自适应显示）
    nodeGroups.append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .style("font-size", (d: any) => {
        // 根据节点大小调整字体大小
        const radius = getNodeRadius(d)
        return Math.max(8, Math.min(12, radius / 3)) + "px"
      })
      .style("font-weight", "600")
      .style("fill", "#fff")
      .style("pointer-events", "none")
      .text(getDisplayText)

    // 节点完整信息提示（悬停时显示）
    nodeGroups.append("title")
      .text((d: any) => `${d.title}\n\n${d.description || '暂无描述'}`)

    // 添加节点交互事件
    nodeGroups
      .on("mouseover", handleNodeMouseOver)
      .on("mouseout", handleNodeMouseOut)
      .on("dblclick", handleNodeDoubleClick)
      .on("click", handleNodeClick)

    console.log('DeductiveTreeGraph: Created', nodeGroups.size(), 'node groups');
    console.log('DeductiveTreeGraph: Node data:', validatedData.nodes.map(n => ({ id: n.id, title: n.title, x: n.x, y: n.y })));

    // 仿真更新函数
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodeGroups
        .attr("transform", (d: any) => `translate(${d.x},${d.y})`)
    })

    console.log('DeductiveTreeGraph: Network rendered with', validatedData.nodes.length, 'nodes and', validatedData.links.length, 'links');
  } catch (error) {
    console.error('DeductiveTreeGraph: Error rendering network:', error);
  }
}

// 将数据转换为网络格式
const buildNetwork = (nodes: AnalysisNode[], connections: NodeConnection[]): { nodes: NetworkNode[], links: NetworkLink[] } => {
  console.log('DeductiveTreeGraph: Building network with nodes:', nodes, 'and connections:', connections);

  // 获取默认尺寸用于初始位置计算
  const defaultWidth = 1200
  const defaultHeight = 800

  // 转换节点，使用更好的初始位置分布
  const networkNodes: NetworkNode[] = nodes.map((node) => {
    // 为不同类型的节点设置不同的初始位置
    let x, y
    if (node.type === 'initial') {
      x = defaultWidth / 2
      y = defaultHeight / 2
    } else if (node.type === 'positive') {
      x = defaultWidth / 2 + (Math.random() - 0.5) * 300
      y = defaultHeight / 2 - 100 + (Math.random() - 0.5) * 200
    } else if (node.type === 'negative') {
      x = defaultWidth / 2 + (Math.random() - 0.5) * 300
      y = defaultHeight / 2 + 100 + (Math.random() - 0.5) * 200
    } else {
      x = defaultWidth / 2 + (Math.random() - 0.5) * 400
      y = defaultHeight / 2 + (Math.random() - 0.5) * 400
    }

    return {
      ...node,
      x,
      y
    }
  })

  // 转换连接
  const networkLinks: NetworkLink[] = connections.map(conn => ({
    ...conn,
    source: conn.from,
    target: conn.to
  }))

  return { nodes: networkNodes, links: networkLinks }
}

// 使用LLM生成更多节点的函数
const generateAdditionalNodes = async (baseNode: NetworkNode, direction: 'positive' | 'negative'): Promise<{ nodes: NetworkNode[], links: NetworkLink[] }> => {
  console.log(`🤖 开始LLM生成: ${direction}方向，基于节点"${baseNode.title}"`)

  try {
    // 获取当前所有节点的上下文
    const currentData = history.value[currentStep.value]
    const contextNodes = currentData.nodes.map(n => `${n.title}: ${n.description}`).join('\n')

    // 构建LLM提示
    const prompt = `基于现有的推演网络，为节点"${baseNode.title}"生成${direction === 'positive' ? '积极' : '负面'}方向的扩展推演。

当前推演网络包含以下节点：
${contextNodes}

请为"${baseNode.title}"生成3个${direction === 'positive' ? '积极' : '负面'}方向的子节点，要求：
1. 与现有节点相关但不重复
2. 具有逻辑性和深度
3. 符合${direction === 'positive' ? '积极' : '负面'}推演的方向

请严格按照以下JSON格式返回：
{
  "nodes": [
    {
      "title": "节点标题",
      "description": "节点描述",
      "impact": "low|medium|high|critical"
    }
  ]
}

只返回JSON，不要其他内容。`

    console.log('📤 发送LLM请求，提示长度:', prompt.length)

    // 调用LLM API
    const { LLMDeductiveAnalyzer } = await import('../utils/deductiveThinking')
    const analyzer = new LLMDeductiveAnalyzer()

    // 详细检查模型配置
    const modelConfig = analyzer.getModelConfig()
    console.log('🔍 检查模型配置:', modelConfig)

    if (!modelConfig) {
      console.warn('⚠️ 没有找到激活的模型配置，使用本地生成')
      throw new Error('NO_ACTIVE_MODEL')
    }

    if (!analyzer.hasValidConfig()) {
      console.warn('⚠️ 激活的模型没有配置API密钥，使用本地生成')
      console.log('📋 当前模型配置:', modelConfig)
      throw new Error('NO_API_KEY')
    }

    console.log('✅ 模型配置验证通过，开始调用LLM API')
    console.log('🔑 使用模型:', modelConfig.name, '提供商:', modelConfig.provider)

    const response = await analyzer.callLLMForExpansion(prompt)
    console.log('📥 LLM原始响应:', response.text)

    // 解析响应
    let parsedResponse
    try {
      // 清理响应文本
      const cleanText = response.text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
      console.log('🧹 清理后的响应:', cleanText)
      parsedResponse = JSON.parse(cleanText)
      console.log('✅ JSON解析成功:', parsedResponse)
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError, '原始文本:', response.text)
      throw new Error('JSON_PARSE_ERROR')
    }

    if (!parsedResponse.nodes || !Array.isArray(parsedResponse.nodes)) {
      console.error('❌ 数据结构验证失败:', parsedResponse)
      throw new Error('INVALID_DATA_STRUCTURE')
    }

    if (parsedResponse.nodes.length === 0) {
      console.warn('⚠️ LLM返回了空节点数组')
      throw new Error('EMPTY_NODES')
    }

    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    // 创建新节点
    parsedResponse.nodes.forEach((nodeData: any, index: number) => {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${index}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: nodeData.title || `${direction}推演${index + 1}`,
        description: nodeData.description || `基于"${baseNode.title}"的推演`,
        level: baseNode.level + 1,
        impact: nodeData.impact || 'medium',
        modelSource: `deductive-${direction}-llm`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      // 创建连接
      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `LLM生成的${direction}推演连接`,
        strength: 0.8,
        source: baseNode.id,
        target: nodeId
      })
    })

    console.log(`🎉 LLM生成成功: 创建了${newNodes.length}个节点`)
    return { nodes: newNodes, links: newLinks }

  } catch (error: any) {
    const errorType = error.message || 'UNKNOWN_ERROR'
    console.error(`❌ LLM生成失败 (${errorType}):`, error)

    // 根据错误类型提供不同的回退策略
    if (errorType === 'NO_API_KEY') {
      console.log('🔄 回退策略: 使用本地生成 (原因: 无API密钥)')
    } else if (errorType === 'JSON_PARSE_ERROR') {
      console.log('🔄 回退策略: 使用本地生成 (原因: JSON解析失败)')
    } else {
      console.log('🔄 回退策略: 使用本地生成 (原因: 其他错误)')
    }

    // 回退到本地生成
    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    for (let i = 0; i < 2; i++) {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${i}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: `${baseNode.title}的${direction === 'positive' ? '积极' : '负面'}影响${i + 1}`,
        description: `基于"${baseNode.title}"的${direction === 'positive' ? '积极' : '负面'}方向推演`,
        level: baseNode.level + 1,
        impact: 'medium',
        modelSource: `deductive-${direction}-local`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `本地生成的${direction}推演连接`,
        strength: 0.7,
        source: baseNode.id,
        target: nodeId
      })
    }

    console.log(`🔧 本地生成完成: 创建了${newNodes.length}个节点`)
    return { nodes: newNodes, links: newLinks }
  }
}

// 节点生成历史记录接口
interface NodeGenerationStep {
  timestamp: number
  action: 'initial' | 'generate-positive' | 'generate-negative' | 'edit' | 'what-if'
  baseNodeId?: string
  newNodeIds: string[]
  newLinkIds: string[]
}

// 节点生成历史
const generationHistory = ref<NodeGenerationStep[]>([])

// 创建时间线步骤 - 改进版本，支持动态节点生成历史
const createNetworkTimelineSteps = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  const { nodes, links } = networkData

  if (nodes.length === 0) {
    return steps
  }

  // 如果有生成历史，按历史重建时间线
  if (generationHistory.value.length > 0) {
    return createTimelineFromHistory(nodes, links)
  }

  // 否则使用默认的层级展示逻辑
  return createDefaultTimeline(nodes, links)
}

// 根据生成历史创建时间线
const createTimelineFromHistory = (allNodes: NetworkNode[], allLinks: NetworkLink[]): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  let currentNodes: NetworkNode[] = []
  let currentLinks: NetworkLink[] = []

  // 按时间顺序重放节点生成历史
  for (const historyStep of generationHistory.value) {
    // 添加新生成的节点
    const newNodes = allNodes.filter(n => historyStep.newNodeIds.includes(n.id))
    currentNodes.push(...newNodes)

    // 添加相关的连接，但只添加两端节点都存在的连接
    const currentNodeIds = new Set(currentNodes.map(n => n.id))
    const newLinks = allLinks.filter(l => {
      const linkId = `${l.from}-${l.to}`
      return historyStep.newLinkIds.includes(linkId) &&
             currentNodeIds.has(l.from) &&
             currentNodeIds.has(l.to)
    })
    currentLinks.push(...newLinks)

    // 验证并清理当前步骤的数据
    const validatedStepData = validateNetworkData({
      nodes: [...currentNodes],
      links: [...currentLinks]
    })

    // 创建当前步骤的快照
    steps.push(validatedStepData)
  }

  return steps
}

// 默认时间线创建逻辑（按层级展示）
const createDefaultTimeline = (nodes: NetworkNode[], links: NetworkLink[]): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []

  // 步骤1: 只显示初始节点
  const initialNodes = nodes.filter(n => n.type === 'initial')
  if (initialNodes.length > 0) {
    const validatedStep1 = validateNetworkData({ nodes: [...initialNodes], links: [] })
    steps.push(validatedStep1)
  }

  // 步骤2: 显示初始节点和第一层节点
  const firstLevelNodes = nodes.filter(n => n.level <= 1)
  if (firstLevelNodes.length > initialNodes.length) {
    const firstLevelLinks = links.filter(l => {
      const sourceNode = nodes.find(n => n.id === l.from)
      const targetNode = nodes.find(n => n.id === l.to)
      return sourceNode && targetNode && sourceNode.level <= 1 && targetNode.level <= 1
    })
    const validatedStep2 = validateNetworkData({ nodes: [...firstLevelNodes], links: [...firstLevelLinks] })
    steps.push(validatedStep2)
  }

  // 步骤3: 显示所有节点
  const validatedStep3 = validateNetworkData({ nodes: [...nodes], links: [...links] })
  steps.push(validatedStep3)

  return steps
}

// 记录节点生成历史
const recordNodeGeneration = (action: NodeGenerationStep['action'], baseNodeId: string | undefined, newNodeIds: string[], newLinkIds: string[]) => {
  const step: NodeGenerationStep = {
    timestamp: Date.now(),
    action,
    baseNodeId,
    newNodeIds,
    newLinkIds
  }
  generationHistory.value.push(step)
  console.log('🕒 记录节点生成历史:', step)
}

// 获取步骤描述
const getStepDescription = (stepIndex: number): string => {
  if (stepIndex < generationHistory.value.length) {
    const step = generationHistory.value[stepIndex]
    switch (step.action) {
      case 'initial':
        return '初始节点'
      case 'generate-positive':
        return `积极推演 (基于: ${step.baseNodeId})`
      case 'generate-negative':
        return `消极推演 (基于: ${step.baseNodeId})`
      case 'edit':
        return '节点编辑'
      default:
        return '未知操作'
    }
  }
  return `步骤 ${stepIndex + 1}`
}

// 获取当前步骤的节点列表
const getCurrentNodes = computed(() => {
  if (currentStep.value < history.value.length) {
    return history.value[currentStep.value].nodes
  }
  return []
})

// 从列表选择节点
const selectNodeFromList = (nodeId: string) => {
  selectedNode.value = nodeId
  // 可选：将节点居中显示
  centerNodeInView(nodeId)
}

// 滚动到列表中对应的项
const scrollToListItem = (nodeId: string) => {
  if (!showNodeList.value) {
    // 如果列表面板未显示，先显示它
    showNodeList.value = true
  }

  // 使用 nextTick 确保 DOM 更新后再滚动
  nextTick(() => {
    const listElement = document.querySelector(`[data-node-id="${nodeId}"]`)
    if (listElement) {
      listElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })

      // 添加一个临时的高亮效果
      listElement.classList.add('highlight-flash')
      setTimeout(() => {
        listElement.classList.remove('highlight-flash')
      }, 1000)
    }
  })
}

// 将节点居中显示
const centerNodeInView = (nodeId: string) => {
  if (!svgRef.value || !zoomBehavior.value) return

  const currentData = history.value[currentStep.value]
  const node = currentData.nodes.find(n => n.id === nodeId)
  if (!node || !node.x || !node.y) return

  const { width, height } = getContainerSize()
  const svg = d3.select(svgRef.value)
  const transform = d3.zoomIdentity
    .translate(width / 2 - node.x, height / 2 - node.y)
    .scale(1.5)

  svg.transition()
    .duration(750)
    .call(zoomBehavior.value.transform, transform)
}

// 切换节点列表显示
const toggleNodeList = () => {
  showNodeList.value = !showNodeList.value
}









// Watch for props changes and update the visualization
watch([() => props.nodes, () => props.connections], ([newNodes, newConnections]) => {
  if (newNodes.length > 0) {
    const networkData = buildNetwork(newNodes, newConnections)
    if (networkData.nodes.length > 0) {
      // 重置生成历史并记录所有节点的生成
      generationHistory.value = []

      // 记录初始节点
      const initialNodeIds = networkData.nodes.filter(n => n.type === 'initial').map(n => n.id)
      if (initialNodeIds.length > 0) {
        recordNodeGeneration('initial', undefined, initialNodeIds, [])
      }

      // 记录积极节点
      const positiveNodeIds = networkData.nodes.filter(n => n.type === 'positive').map(n => n.id)
      if (positiveNodeIds.length > 0) {
        const positiveLinkIds = networkData.links
          .filter(l => {
            const fromNode = networkData.nodes.find(n => n.id === l.from)
            const toNode = networkData.nodes.find(n => n.id === l.to)
            return (fromNode?.type === 'initial' && toNode?.type === 'positive') ||
                   (fromNode?.type === 'positive' && toNode?.type === 'positive')
          })
          .map(l => `${l.from}-${l.to}`)
        recordNodeGeneration('generate-positive', initialNodeIds[0], positiveNodeIds, positiveLinkIds)
      }

      // 记录负面节点
      const negativeNodeIds = networkData.nodes.filter(n => n.type === 'negative').map(n => n.id)
      if (negativeNodeIds.length > 0) {
        const negativeLinkIds = networkData.links
          .filter(l => {
            const fromNode = networkData.nodes.find(n => n.id === l.from)
            const toNode = networkData.nodes.find(n => n.id === l.to)
            return (fromNode?.type === 'initial' && toNode?.type === 'negative') ||
                   (fromNode?.type === 'negative' && toNode?.type === 'negative')
          })
          .map(l => `${l.from}-${l.to}`)
        recordNodeGeneration('generate-negative', initialNodeIds[0], negativeNodeIds, negativeLinkIds)
      }

      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      // 设置为最后一步，显示所有节点
      currentStep.value = Math.max(0, history.value.length - 1)

      // 延迟渲染，确保DOM准备就绪
      nextTick(() => {
        setTimeout(() => {
          renderNetwork(history.value[currentStep.value])
        }, 100)
      })
    }
  }
}, { immediate: true, deep: true })

// Navigation functions
const canGoBack = computed(() => currentStep.value > 0)
const canGoForward = computed(() => currentStep.value < history.value.length - 1)

const navigateHistory = (step: number) => {
  if (step >= 0 && step < history.value.length) {
    currentStep.value = step
    const networkData = history.value[currentStep.value]
    if (networkData) {
      nextTick(() => {
        renderNetwork(networkData)
      })
    }
  }
}

const goBack = () => {
  if (canGoBack.value) {
    navigateHistory(currentStep.value - 1)
  }
}

const goForward = () => {
  if (canGoForward.value) {
    navigateHistory(currentStep.value + 1)
  }
}

// Node editing functions
const saveNodeEdit = () => {
  if (editingNode.value && editingText.value.trim()) {
    const currentData = history.value[currentStep.value]
    const nodeToUpdate = currentData.nodes.find(n => n.id === editingNode.value)
    if (nodeToUpdate) {
      const oldTitle = nodeToUpdate.title
      nodeToUpdate.title = editingText.value.trim()

      // 记录编辑历史（如果标题确实发生了变化）
      if (oldTitle !== nodeToUpdate.title) {
        recordNodeGeneration('edit', nodeToUpdate.id, [nodeToUpdate.id], [])

        // 更新时间线以反映编辑
        const allNodes = [...currentData.nodes]
        const allLinks = [...currentData.links]
        const networkData = { nodes: allNodes, links: allLinks }
        history.value = createNetworkTimelineSteps(networkData)
        maxSteps.value = history.value.length
      }

      nextTick(() => {
        renderNetwork(currentData)
      })
      emit('nodes-updated', currentData.nodes)
    }
  }
  editingNode.value = null
  editingText.value = ''
}

const cancelNodeEdit = () => {
  editingNode.value = null
  editingText.value = ''
}

// Node generation functions
const generatePositiveNodes = async (baseNode: NetworkNode) => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    showNodeActions.value = false

    const newData = await generateAdditionalNodes(baseNode, 'positive')

    // 记录节点生成历史
    const newNodeIds = newData.nodes.map(n => n.id)
    const newLinkIds = newData.links.map(l => `${l.from}-${l.to}`)
    recordNodeGeneration('generate-positive', baseNode.id, newNodeIds, newLinkIds)

    // 更新当前数据
    const currentData = history.value[currentStep.value]
    currentData.nodes.push(...newData.nodes)
    currentData.links.push(...newData.links)

    // 重新创建时间线步骤以包含新的生成历史
    const allNodes = [...currentData.nodes]
    const allLinks = [...currentData.links]
    const networkData = { nodes: allNodes, links: allLinks }
    history.value = createNetworkTimelineSteps(networkData)
    maxSteps.value = history.value.length

    // 跳转到最新步骤
    currentStep.value = history.value.length - 1

    nextTick(() => {
      renderNetwork(history.value[currentStep.value])
    })
    emit('nodes-updated', allNodes)
    emit('connections-updated', allLinks)
  } catch (error) {
    console.error('Failed to generate positive nodes:', error)
  } finally {
    isGenerating.value = false
  }
}

const generateNegativeNodes = async (baseNode: NetworkNode) => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    showNodeActions.value = false

    const newData = await generateAdditionalNodes(baseNode, 'negative')

    // 记录节点生成历史
    const newNodeIds = newData.nodes.map(n => n.id)
    const newLinkIds = newData.links.map(l => `${l.from}-${l.to}`)
    recordNodeGeneration('generate-negative', baseNode.id, newNodeIds, newLinkIds)

    // 更新当前数据
    const currentData = history.value[currentStep.value]
    currentData.nodes.push(...newData.nodes)
    currentData.links.push(...newData.links)

    // 重新创建时间线步骤以包含新的生成历史
    const allNodes = [...currentData.nodes]
    const allLinks = [...currentData.links]
    const networkData = { nodes: allNodes, links: allLinks }
    history.value = createNetworkTimelineSteps(networkData)
    maxSteps.value = history.value.length

    // 跳转到最新步骤
    currentStep.value = history.value.length - 1

    nextTick(() => {
      renderNetwork(history.value[currentStep.value])
    })

    // 保存扩展数据到本地存储
    saveExtendedDataToStorage(allNodes, allLinks)

    emit('nodes-updated', allNodes)
    emit('connections-updated', allLinks)
  } catch (error) {
    console.error('Failed to generate negative nodes:', error)
  } finally {
    isGenerating.value = false
  }
}

// What If 功能
const openWhatIfDialog = (baseNode: NetworkNode) => {
  whatIfBaseNode.value = baseNode
  whatIfScenario.value = ''
  showWhatIfDialog.value = true
  showNodeActions.value = false
}

const closeWhatIfDialog = () => {
  showWhatIfDialog.value = false
  whatIfBaseNode.value = null
  whatIfScenario.value = ''
}

const generateWhatIfNodes = async () => {
  if (!whatIfBaseNode.value || !whatIfScenario.value.trim() || isWhatIfGenerating.value) return

  try {
    isWhatIfGenerating.value = true

    const baseNode = whatIfBaseNode.value
    const scenario = whatIfScenario.value.trim()

    // 构建What If提示
    const prompt = `基于节点"${baseNode.title}"，在假设情况"${scenario}"下，生成3个相关的推演节点。

当前节点信息：
标题：${baseNode.title}
描述：${baseNode.description || '无描述'}

假设情况：${scenario}

请分析在这种假设情况下可能产生的影响、结果或变化，生成3个相关节点。

请严格按照以下JSON格式返回：
{
  "nodes": [
    {
      "id": "whatif-1",
      "title": "节点标题",
      "description": "详细描述",
      "level": 2,
      "impact": "high|medium|low|critical",
      "type": "positive|negative"
    }
  ],
  "connections": [
    {
      "from": "${baseNode.id}",
      "to": "whatif-1",
      "type": "strong|weak",
      "description": "连接描述",
      "strength": 0.8
    }
  ]
}`

    console.log('🤔 What If 提示:', prompt)

    // 调用LLM API
    const { LLMDeductiveAnalyzer } = await import('../utils/deductiveThinking')
    const analyzer = new LLMDeductiveAnalyzer()

    if (!analyzer.hasValidConfig()) {
      throw new Error('需要配置LLM API才能使用What If功能')
    }

    const { text } = await analyzer.callLLMForExpansion(prompt)
    console.log('🤔 What If 响应:', text)

    // 解析响应
    const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
    const parsedResult = JSON.parse(cleanText)

    // 转换为网络节点格式
    const newNodes: NetworkNode[] = parsedResult.nodes.map((node: any, index: number) => ({
      ...node,
      id: `whatif-${Date.now()}-${index}`,
      modelSource: 'what-if',
      x: baseNode.x! + (Math.random() - 0.5) * 200,
      y: baseNode.y! + (Math.random() - 0.5) * 200
    }))

    const newLinks: NetworkLink[] = parsedResult.connections.map((conn: any, index: number) => {
      const targetNode = newNodes[index] || newNodes[0]
      return {
        ...conn,
        from: baseNode.id,
        to: targetNode.id,
        source: baseNode.id,
        target: targetNode.id
      }
    })

    // 记录节点生成历史
    const newNodeIds = newNodes.map(n => n.id)
    const newLinkIds = newLinks.map(l => `${l.from}-${l.to}`)
    recordNodeGeneration('what-if', baseNode.id, newNodeIds, newLinkIds)

    // 更新当前数据
    const currentData = history.value[currentStep.value]
    currentData.nodes.push(...newNodes)
    currentData.links.push(...newLinks)

    // 重新创建时间线步骤
    const allNodes = [...currentData.nodes]
    const allLinks = [...currentData.links]
    const networkData = { nodes: allNodes, links: allLinks }
    history.value = createNetworkTimelineSteps(networkData)
    maxSteps.value = history.value.length

    // 跳转到最新步骤
    currentStep.value = history.value.length - 1

    nextTick(() => {
      renderNetwork(history.value[currentStep.value])
    })

    // 保存扩展数据到本地存储
    saveExtendedDataToStorage(allNodes, allLinks)

    emit('nodes-updated', allNodes)
    emit('connections-updated', allLinks)

    // 关闭对话框
    closeWhatIfDialog()

  } catch (error) {
    console.error('What If 生成失败:', error)
    alert('What If 生成失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    isWhatIfGenerating.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  if (props.nodes.length > 0) {
    let networkData = buildNetwork(props.nodes, props.connections)

    // 尝试加载扩展数据
    const extendedData = loadExtendedDataFromStorage()
    if (extendedData && extendedData.nodes.length > props.nodes.length) {
      console.log('DeductiveTreeGraph: Loading extended data from storage')
      networkData = buildNetwork(extendedData.nodes, extendedData.connections)
    }

    if (networkData.nodes.length > 0) {
      // 重置生成历史并记录所有节点的生成
      generationHistory.value = []

      // 记录初始节点
      const initialNodeIds = networkData.nodes.filter(n => n.type === 'initial').map(n => n.id)
      if (initialNodeIds.length > 0) {
        recordNodeGeneration('initial', undefined, initialNodeIds, [])
      }

      // 记录积极节点
      const positiveNodeIds = networkData.nodes.filter(n => n.type === 'positive').map(n => n.id)
      if (positiveNodeIds.length > 0) {
        const positiveLinkIds = networkData.links
          .filter(l => {
            const fromNode = networkData.nodes.find(n => n.id === l.from)
            const toNode = networkData.nodes.find(n => n.id === l.to)
            return (fromNode?.type === 'initial' && toNode?.type === 'positive') ||
                   (fromNode?.type === 'positive' && toNode?.type === 'positive')
          })
          .map(l => `${l.from}-${l.to}`)
        recordNodeGeneration('generate-positive', initialNodeIds[0], positiveNodeIds, positiveLinkIds)
      }

      // 记录负面节点
      const negativeNodeIds = networkData.nodes.filter(n => n.type === 'negative').map(n => n.id)
      if (negativeNodeIds.length > 0) {
        const negativeLinkIds = networkData.links
          .filter(l => {
            const fromNode = networkData.nodes.find(n => n.id === l.from)
            const toNode = networkData.nodes.find(n => n.id === l.to)
            return (fromNode?.type === 'initial' && toNode?.type === 'negative') ||
                   (fromNode?.type === 'negative' && toNode?.type === 'negative')
          })
          .map(l => `${l.from}-${l.to}`)
        recordNodeGeneration('generate-negative', initialNodeIds[0], negativeNodeIds, negativeLinkIds)
      }

      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      // 设置为最后一步，显示所有节点
      currentStep.value = Math.max(0, history.value.length - 1)

      // 延迟渲染，确保DOM准备就绪
      nextTick(() => {
        setTimeout(() => {
          renderNetwork(history.value[currentStep.value])
        }, 100)
      })
    }
  }
})

onUnmounted(() => {
  if (renderTimeout.value) {
    clearTimeout(renderTimeout.value)
  }
  if (svgRef.value) {
    d3.select(svgRef.value).selectAll('*').remove()
  }
  showNodeActions.value = false
  editingNode.value = null
  hoveredNode.value = null
  isGenerating.value = false
})
</script>

<template>
  <div class="deductive-tree-graph relative w-full h-full flex">
    <!-- 主图区域 -->
    <div class="flex-1 relative" :class="{ 'pr-80': showNodeList }">
      <svg v-if="history.length > 0 && history[currentStep]" ref="svgRef" class="w-full h-full"></svg>

    <!-- 节点编辑弹窗 -->
    <div v-if="editingNode" class="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-xl max-w-sm w-full mx-4">
        <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-3">编辑节点</h3>
        <input
          v-model="editingText"
          type="text"
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          placeholder="输入节点标题"
          @keyup.enter="saveNodeEdit"
          @keyup.escape="cancelNodeEdit"
          autofocus
        />
        <div class="flex justify-end space-x-2 mt-3">
          <button
            @click="cancelNodeEdit"
            class="px-3 py-1 text-xs text-slate-600 hover:text-slate-800 rounded"
          >
            取消
          </button>
          <button
            @click="saveNodeEdit"
            class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- What If 对话框 -->
    <div v-if="showWhatIfDialog" class="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-2xl max-w-md w-full mx-4">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">What If 假设推演</h3>
            <p class="text-sm text-slate-600 dark:text-slate-400">基于"{{ whatIfBaseNode?.title }}"进行假设推演</p>
          </div>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            假设情况描述
          </label>
          <textarea
            v-model="whatIfScenario"
            class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm resize-none"
            rows="4"
            placeholder="请描述您想要探索的假设情况，例如：如果技术成本降低50%会怎样？"
            @keyup.ctrl.enter="generateWhatIfNodes"
          ></textarea>
          <p class="text-xs text-slate-500 dark:text-slate-400 mt-1">
            提示：描述一个具体的假设情况，AI将基于此生成相关的推演节点
          </p>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="closeWhatIfDialog"
            :disabled="isWhatIfGenerating"
            class="px-4 py-2 text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 disabled:opacity-50"
          >
            取消
          </button>
          <button
            @click="generateWhatIfNodes"
            :disabled="!whatIfScenario.trim() || isWhatIfGenerating"
            class="flex items-center space-x-2 px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="isWhatIfGenerating" class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></span>
            <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            <span>{{ isWhatIfGenerating ? '生成中...' : '开始推演' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 扁平化节点操作菜单 -->
    <div
      v-if="showNodeActions && hoveredNode"
      class="node-actions-menu absolute z-50 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-2 min-w-[140px]"
      :style="{ left: nodeActionPosition.x + 'px', top: nodeActionPosition.y + 'px' }"
      @mouseenter="handleMenuMouseEnter"
      @mouseleave="handleMenuMouseLeave"
    >
      <button
        @click="generatePositiveNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        :disabled="isGenerating"
        class="flex items-center w-full text-left px-3 py-2 text-sm text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        title="生成积极方向"
      >
        <span v-if="isGenerating" class="inline-block w-3 h-3 border border-green-600 border-t-transparent rounded-full animate-spin mr-2"></span>
        <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        积极推演
      </button>
      <button
        @click="generateNegativeNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        :disabled="isGenerating"
        class="flex items-center w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        title="生成消极方向"
      >
        <span v-if="isGenerating" class="inline-block w-3 h-3 border border-red-600 border-t-transparent rounded-full animate-spin mr-2"></span>
        <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
        </svg>
        消极推演
      </button>
      <div class="border-t border-slate-200 dark:border-slate-600 my-1"></div>
      <button
        @click="openWhatIfDialog(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        :disabled="isWhatIfGenerating"
        class="flex items-center w-full text-left px-3 py-2 text-sm text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        title="What If 假设推演"
      >
        <span v-if="isWhatIfGenerating" class="inline-block w-3 h-3 border border-purple-600 border-t-transparent rounded-full animate-spin mr-2"></span>
        <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        What If
      </button>
      <div class="border-t border-slate-200 dark:border-slate-600 my-1"></div>
      <button
        @click="handleNodeDoubleClick($event, history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="flex items-center w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
        title="编辑节点"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        编辑节点
      </button>
    </div>

    <!-- 扁平化控制工具栏 -->
    <div v-if="history.length > 0" class="absolute bottom-6 left-1/2 -translate-x-1/2 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
      <div class="flex items-center divide-x divide-slate-200 dark:divide-slate-700">
        <!-- 生成状态区域 -->
        <div v-if="isGenerating" class="px-4 py-3 flex items-center space-x-3">
          <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm font-medium text-blue-600 dark:text-blue-400">AI推演中</span>
        </div>

        <!-- 时间线导航区域 -->
        <div class="px-4 py-3 flex items-center space-x-3">
          <button
            @click="goBack"
            :disabled="!canGoBack || isGenerating"
            class="w-8 h-8 bg-blue-500 text-white rounded disabled:bg-slate-400 hover:bg-blue-600 transition-colors flex items-center justify-center"
            title="上一步"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <!-- 扁平化步骤指示器 -->
          <div class="flex items-center space-x-2 px-3 py-2 bg-slate-100 dark:bg-slate-700 rounded">
            <div class="flex space-x-1">
              <div
                v-for="(_, index) in Math.min(history.length, 8)"
                :key="index"
                :class="[
                  'w-2 h-2 rounded-full transition-all cursor-pointer',
                  index === currentStep
                    ? 'bg-blue-500'
                    : index < currentStep
                      ? 'bg-green-500'
                      : 'bg-slate-300 dark:bg-slate-600 hover:bg-slate-400'
                ]"
                @click="navigateHistory(index)"
                :title="`步骤 ${index + 1}: ${getStepDescription(index)}`"
              ></div>
              <div v-if="history.length > 8" class="text-xs text-slate-500 dark:text-slate-400 ml-2">
                +{{ history.length - 8 }}
              </div>
            </div>
            <div class="text-xs font-medium text-slate-600 dark:text-slate-400 ml-2">
              {{ currentStep + 1 }}/{{ history.length }}
            </div>
          </div>

          <button
            @click="goForward"
            :disabled="!canGoForward || isGenerating"
            class="w-8 h-8 bg-blue-500 text-white rounded disabled:bg-slate-400 hover:bg-blue-600 transition-colors flex items-center justify-center"
            title="下一步"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>

        <!-- 视图控制区域 -->
        <div class="px-4 py-3 flex items-center space-x-1">
          <button
            @click="zoomOut"
            class="w-8 h-8 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600 rounded transition-colors flex items-center justify-center"
            title="缩小"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
            </svg>
          </button>
          <button
            @click="resetZoom"
            class="w-8 h-8 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600 rounded transition-colors flex items-center justify-center"
            title="重置视图"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
            </svg>
          </button>
          <button
            @click="zoomIn"
            class="w-8 h-8 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600 rounded transition-colors flex items-center justify-center"
            title="放大"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

      <div v-else class="flex items-center justify-center w-full h-full text-slate-400">
        <div class="text-center">
          <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p class="text-sm">构建推演网络中...</p>
        </div>
      </div>
    </div>

    <!-- 扁平化右侧节点列表面板 -->
    <div v-if="showNodeList && history.length > 0" class="absolute right-0 top-0 w-80 h-full bg-white dark:bg-slate-800 border-l border-slate-200 dark:border-slate-700">
      <div class="flex flex-col h-full">
        <!-- 扁平化面板头部 -->
        <div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-indigo-500 rounded flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">推演节点</h3>
              <p class="text-xs text-slate-500 dark:text-slate-400">{{ getCurrentNodes.length }} 个节点</p>
            </div>
          </div>
          <button
            @click="toggleNodeList"
            class="w-8 h-8 bg-white dark:bg-slate-600 hover:bg-slate-100 dark:hover:bg-slate-500 rounded flex items-center justify-center transition-colors"
            title="隐藏面板"
          >
            <svg class="w-4 h-4 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- 扁平化节点列表 -->
        <div class="flex-1 overflow-y-auto p-4 space-y-3">
          <div
            v-for="node in getCurrentNodes"
            :key="node.id"
            :data-node-id="node.id"
            :class="[
              'p-4 rounded-lg border cursor-pointer transition-all',
              selectedNode === node.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-slate-200 dark:border-slate-600 bg-white dark:bg-slate-700 hover:border-slate-300 dark:hover:border-slate-500'
            ]"
            @click="selectNodeFromList(node.id)"
          >
            <!-- 节点头部 -->
            <div class="flex items-start space-x-3 mb-3">
              <div :class="[
                'w-4 h-4 rounded-full mt-0.5 flex-shrink-0',
                node.type === 'initial' ? 'bg-indigo-500' :
                node.type === 'positive' ? 'bg-green-500' :
                node.type === 'negative' ? 'bg-red-500' : 'bg-slate-500'
              ]"></div>

              <div class="flex-1 min-w-0">
                <!-- 节点标题 -->
                <h4 class="font-semibold text-slate-800 dark:text-slate-200 text-base leading-tight mb-2">
                  {{ node.title }}
                </h4>

                <!-- 节点标签组 -->
                <div class="flex flex-wrap gap-2 mb-2">
                  <span :class="[
                    'px-2 py-1 rounded text-xs font-medium',
                    node.type === 'initial' ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300' :
                    node.type === 'positive' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' :
                    node.type === 'negative' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                    'bg-slate-100 text-slate-700 dark:bg-slate-900/30 dark:text-slate-300'
                  ]">
                    {{ node.type === 'initial' ? '初始节点' :
                        node.type === 'positive' ? '积极推演' :
                        node.type === 'negative' ? '消极推演' : '其他' }}
                  </span>

                  <span :class="[
                    'px-2 py-1 rounded text-xs font-medium',
                    node.impact === 'critical' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                    node.impact === 'high' ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300' :
                    node.impact === 'medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                  ]">
                    {{ node.impact === 'critical' ? '关键影响' :
                        node.impact === 'high' ? '高影响' :
                        node.impact === 'medium' ? '中等影响' : '低影响' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 节点描述 -->
            <div v-if="node.description" class="mb-3">
              <p class="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">
                {{ node.description }}
              </p>
            </div>

            <!-- 节点洞察 -->
            <div v-if="node.insights && node.insights.length > 0" class="mb-3">
              <h5 class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-2">关键洞察</h5>
              <div class="space-y-1">
                <div
                  v-for="insight in node.insights"
                  :key="insight"
                  class="text-xs text-slate-600 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 p-2 rounded"
                >
                  {{ insight }}
                </div>
              </div>
            </div>

            <!-- 节点元信息 -->
            <div class="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400 pt-2 border-t border-slate-100 dark:border-slate-600">
              <span>层级: {{ node.level }}</span>
              <span>来源: {{ node.modelSource }}</span>
            </div>
          </div>
        </div>

        <!-- 扁平化面板底部统计 -->
        <div class="p-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-700">
          <div class="grid grid-cols-2 gap-4 text-xs">
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ getCurrentNodes.length }}</div>
              <div class="text-slate-600 dark:text-slate-400">节点总数</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ currentStep + 1 }}</div>
              <div class="text-slate-600 dark:text-slate-400">当前步骤</div>
            </div>
          </div>

          <!-- 节点类型统计 -->
          <div class="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600">
            <div class="flex justify-between items-center text-xs">
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-indigo-500 rounded-full"></div>
                <span class="text-slate-600 dark:text-slate-400">初始: {{ getCurrentNodes.filter(n => n.type === 'initial').length }}</span>
              </div>
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-slate-600 dark:text-slate-400">积极: {{ getCurrentNodes.filter(n => n.type === 'positive').length }}</span>
              </div>
              <div class="flex items-center space-x-1">
                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                <span class="text-slate-600 dark:text-slate-400">消极: {{ getCurrentNodes.filter(n => n.type === 'negative').length }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 扁平化节点列表切换按钮（当面板隐藏时显示） -->
    <button
      v-if="!showNodeList && history.length > 0"
      @click="toggleNodeList"
      class="absolute top-4 right-4 w-10 h-10 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors flex items-center justify-center"
      title="显示节点列表"
    >
      <svg class="w-5 h-5 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
      </svg>
    </button>
  </div>
</template>

<style scoped>
/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 节点列表滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 深色模式下的滚动条 */
.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.3);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.5);
}

/* 节点操作菜单动画 */
.node-actions-menu {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 高亮闪烁效果 */
.highlight-flash {
  animation: highlightFlash 1s ease-out;
}

@keyframes highlightFlash {
  0% {
    background-color: rgba(59, 130, 246, 0.3);
    transform: scale(1);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.5);
    transform: scale(1.02);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}

/* 深色模式下的高亮闪烁效果 */
.dark .highlight-flash {
  animation: highlightFlashDark 1s ease-out;
}

@keyframes highlightFlashDark {
  0% {
    background-color: rgba(59, 130, 246, 0.2);
    transform: scale(1);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.4);
    transform: scale(1.02);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
  }
}
</style>
